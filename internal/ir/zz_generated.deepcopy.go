//go:build !ignore_autogenerated

// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

// Code generated by controller-gen. DO NOT EDIT.

package ir

import (
	"github.com/envoyproxy/gateway/api/v1alpha1"
	apiextensionsv1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/gateway-api/apis/v1alpha2"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ALSAccessLog) DeepCopyInto(out *ALSAccessLog) {
	*out = *in
	if in.CELMatches != nil {
		in, out := &in.CELMatches, &out.CELMatches
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	in.Destination.DeepCopyInto(&out.Destination)
	if in.Traffic != nil {
		in, out := &in.Traffic, &out.Traffic
		*out = new(TrafficFeatures)
		(*in).DeepCopyInto(*out)
	}
	if in.Text != nil {
		in, out := &in.Text, &out.Text
		*out = new(string)
		**out = **in
	}
	if in.Attributes != nil {
		in, out := &in.Attributes, &out.Attributes
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(ALSAccessLogHTTP)
		(*in).DeepCopyInto(*out)
	}
	if in.LogType != nil {
		in, out := &in.LogType, &out.LogType
		*out = new(ProxyAccessLogType)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ALSAccessLog.
func (in *ALSAccessLog) DeepCopy() *ALSAccessLog {
	if in == nil {
		return nil
	}
	out := new(ALSAccessLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ALSAccessLogHTTP) DeepCopyInto(out *ALSAccessLogHTTP) {
	*out = *in
	if in.RequestHeaders != nil {
		in, out := &in.RequestHeaders, &out.RequestHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ResponseHeaders != nil {
		in, out := &in.ResponseHeaders, &out.ResponseHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ResponseTrailers != nil {
		in, out := &in.ResponseTrailers, &out.ResponseTrailers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ALSAccessLogHTTP.
func (in *ALSAccessLogHTTP) DeepCopy() *ALSAccessLogHTTP {
	if in == nil {
		return nil
	}
	out := new(ALSAccessLogHTTP)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *APIKeyAuth) DeepCopyInto(out *APIKeyAuth) {
	*out = *in
	if in.Credentials != nil {
		in, out := &in.Credentials, &out.Credentials
		*out = make(map[string]PrivateBytes, len(*in))
		for key, val := range *in {
			var outVal []byte
			if val == nil {
				(*out)[key] = nil
			} else {
				inVal := (*in)[key]
				in, out := &inVal, &outVal
				*out = make(PrivateBytes, len(*in))
				copy(*out, *in)
			}
			(*out)[key] = outVal
		}
	}
	if in.ExtractFrom != nil {
		in, out := &in.ExtractFrom, &out.ExtractFrom
		*out = make([]*ExtractFrom, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(ExtractFrom)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new APIKeyAuth.
func (in *APIKeyAuth) DeepCopy() *APIKeyAuth {
	if in == nil {
		return nil
	}
	out := new(APIKeyAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AccessLog) DeepCopyInto(out *AccessLog) {
	*out = *in
	if in.Text != nil {
		in, out := &in.Text, &out.Text
		*out = make([]*TextAccessLog, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(TextAccessLog)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.JSON != nil {
		in, out := &in.JSON, &out.JSON
		*out = make([]*JSONAccessLog, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(JSONAccessLog)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.ALS != nil {
		in, out := &in.ALS, &out.ALS
		*out = make([]*ALSAccessLog, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(ALSAccessLog)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.OpenTelemetry != nil {
		in, out := &in.OpenTelemetry, &out.OpenTelemetry
		*out = make([]*OpenTelemetryAccessLog, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(OpenTelemetryAccessLog)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AccessLog.
func (in *AccessLog) DeepCopy() *AccessLog {
	if in == nil {
		return nil
	}
	out := new(AccessLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ActiveHealthCheck) DeepCopyInto(out *ActiveHealthCheck) {
	*out = *in
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(v1.Duration)
		**out = **in
	}
	if in.Interval != nil {
		in, out := &in.Interval, &out.Interval
		*out = new(v1.Duration)
		**out = **in
	}
	if in.UnhealthyThreshold != nil {
		in, out := &in.UnhealthyThreshold, &out.UnhealthyThreshold
		*out = new(uint32)
		**out = **in
	}
	if in.HealthyThreshold != nil {
		in, out := &in.HealthyThreshold, &out.HealthyThreshold
		*out = new(uint32)
		**out = **in
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(HTTPHealthChecker)
		(*in).DeepCopyInto(*out)
	}
	if in.TCP != nil {
		in, out := &in.TCP, &out.TCP
		*out = new(TCPHealthChecker)
		(*in).DeepCopyInto(*out)
	}
	if in.GRPC != nil {
		in, out := &in.GRPC, &out.GRPC
		*out = new(GRPCHealthChecker)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ActiveHealthCheck.
func (in *ActiveHealthCheck) DeepCopy() *ActiveHealthCheck {
	if in == nil {
		return nil
	}
	out := new(ActiveHealthCheck)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AddHeader) DeepCopyInto(out *AddHeader) {
	*out = *in
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AddHeader.
func (in *AddHeader) DeepCopy() *AddHeader {
	if in == nil {
		return nil
	}
	out := new(AddHeader)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Authorization) DeepCopyInto(out *Authorization) {
	*out = *in
	if in.Rules != nil {
		in, out := &in.Rules, &out.Rules
		*out = make([]*AuthorizationRule, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(AuthorizationRule)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Authorization.
func (in *Authorization) DeepCopy() *Authorization {
	if in == nil {
		return nil
	}
	out := new(Authorization)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AuthorizationRule) DeepCopyInto(out *AuthorizationRule) {
	*out = *in
	if in.Operation != nil {
		in, out := &in.Operation, &out.Operation
		*out = new(v1alpha1.Operation)
		(*in).DeepCopyInto(*out)
	}
	in.Principal.DeepCopyInto(&out.Principal)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AuthorizationRule.
func (in *AuthorizationRule) DeepCopy() *AuthorizationRule {
	if in == nil {
		return nil
	}
	out := new(AuthorizationRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackOffPolicy) DeepCopyInto(out *BackOffPolicy) {
	*out = *in
	if in.BaseInterval != nil {
		in, out := &in.BaseInterval, &out.BaseInterval
		*out = new(v1.Duration)
		**out = **in
	}
	if in.MaxInterval != nil {
		in, out := &in.MaxInterval, &out.MaxInterval
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackOffPolicy.
func (in *BackOffPolicy) DeepCopy() *BackOffPolicy {
	if in == nil {
		return nil
	}
	out := new(BackOffPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BackendConnection) DeepCopyInto(out *BackendConnection) {
	*out = *in
	if in.BufferLimitBytes != nil {
		in, out := &in.BufferLimitBytes, &out.BufferLimitBytes
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BackendConnection.
func (in *BackendConnection) DeepCopy() *BackendConnection {
	if in == nil {
		return nil
	}
	out := new(BackendConnection)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BasicAuth) DeepCopyInto(out *BasicAuth) {
	*out = *in
	if in.Users != nil {
		in, out := &in.Users, &out.Users
		*out = make(PrivateBytes, len(*in))
		copy(*out, *in)
	}
	if in.ForwardUsernameHeader != nil {
		in, out := &in.ForwardUsernameHeader, &out.ForwardUsernameHeader
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BasicAuth.
func (in *BasicAuth) DeepCopy() *BasicAuth {
	if in == nil {
		return nil
	}
	out := new(BasicAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BodyToExtAuth) DeepCopyInto(out *BodyToExtAuth) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BodyToExtAuth.
func (in *BodyToExtAuth) DeepCopy() *BodyToExtAuth {
	if in == nil {
		return nil
	}
	out := new(BodyToExtAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CORS) DeepCopyInto(out *CORS) {
	*out = *in
	if in.AllowOrigins != nil {
		in, out := &in.AllowOrigins, &out.AllowOrigins
		*out = make([]*StringMatch, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(StringMatch)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.AllowMethods != nil {
		in, out := &in.AllowMethods, &out.AllowMethods
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AllowHeaders != nil {
		in, out := &in.AllowHeaders, &out.AllowHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ExposeHeaders != nil {
		in, out := &in.ExposeHeaders, &out.ExposeHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.MaxAge != nil {
		in, out := &in.MaxAge, &out.MaxAge
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CORS.
func (in *CORS) DeepCopy() *CORS {
	if in == nil {
		return nil
	}
	out := new(CORS)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CircuitBreaker) DeepCopyInto(out *CircuitBreaker) {
	*out = *in
	if in.MaxConnections != nil {
		in, out := &in.MaxConnections, &out.MaxConnections
		*out = new(uint32)
		**out = **in
	}
	if in.MaxPendingRequests != nil {
		in, out := &in.MaxPendingRequests, &out.MaxPendingRequests
		*out = new(uint32)
		**out = **in
	}
	if in.MaxParallelRequests != nil {
		in, out := &in.MaxParallelRequests, &out.MaxParallelRequests
		*out = new(uint32)
		**out = **in
	}
	if in.MaxRequestsPerConnection != nil {
		in, out := &in.MaxRequestsPerConnection, &out.MaxRequestsPerConnection
		*out = new(uint32)
		**out = **in
	}
	if in.MaxParallelRetries != nil {
		in, out := &in.MaxParallelRetries, &out.MaxParallelRetries
		*out = new(uint32)
		**out = **in
	}
	if in.PerEndpoint != nil {
		in, out := &in.PerEndpoint, &out.PerEndpoint
		*out = new(PerEndpointCircuitBreakers)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CircuitBreaker.
func (in *CircuitBreaker) DeepCopy() *CircuitBreaker {
	if in == nil {
		return nil
	}
	out := new(CircuitBreaker)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientConnection) DeepCopyInto(out *ClientConnection) {
	*out = *in
	if in.ConnectionLimit != nil {
		in, out := &in.ConnectionLimit, &out.ConnectionLimit
		*out = new(ConnectionLimit)
		(*in).DeepCopyInto(*out)
	}
	if in.BufferLimitBytes != nil {
		in, out := &in.BufferLimitBytes, &out.BufferLimitBytes
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientConnection.
func (in *ClientConnection) DeepCopy() *ClientConnection {
	if in == nil {
		return nil
	}
	out := new(ClientConnection)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientIPDetectionSettings) DeepCopyInto(out *ClientIPDetectionSettings) {
	*out = *in
	if in.XForwardedFor != nil {
		in, out := &in.XForwardedFor, &out.XForwardedFor
		*out = new(v1alpha1.XForwardedForSettings)
		(*in).DeepCopyInto(*out)
	}
	if in.CustomHeader != nil {
		in, out := &in.CustomHeader, &out.CustomHeader
		*out = new(v1alpha1.CustomHeaderExtensionSettings)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientIPDetectionSettings.
func (in *ClientIPDetectionSettings) DeepCopy() *ClientIPDetectionSettings {
	if in == nil {
		return nil
	}
	out := new(ClientIPDetectionSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClientTimeout) DeepCopyInto(out *ClientTimeout) {
	*out = *in
	if in.TCP != nil {
		in, out := &in.TCP, &out.TCP
		*out = new(TCPClientTimeout)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(HTTPClientTimeout)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClientTimeout.
func (in *ClientTimeout) DeepCopy() *ClientTimeout {
	if in == nil {
		return nil
	}
	out := new(ClientTimeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Compression) DeepCopyInto(out *Compression) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Compression.
func (in *Compression) DeepCopy() *Compression {
	if in == nil {
		return nil
	}
	out := new(Compression)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConnectConfig) DeepCopyInto(out *ConnectConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConnectConfig.
func (in *ConnectConfig) DeepCopy() *ConnectConfig {
	if in == nil {
		return nil
	}
	out := new(ConnectConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConnectionLimit) DeepCopyInto(out *ConnectionLimit) {
	*out = *in
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = new(uint64)
		**out = **in
	}
	if in.CloseDelay != nil {
		in, out := &in.CloseDelay, &out.CloseDelay
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConnectionLimit.
func (in *ConnectionLimit) DeepCopy() *ConnectionLimit {
	if in == nil {
		return nil
	}
	out := new(ConnectionLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConsistentHash) DeepCopyInto(out *ConsistentHash) {
	*out = *in
	if in.SourceIP != nil {
		in, out := &in.SourceIP, &out.SourceIP
		*out = new(bool)
		**out = **in
	}
	if in.Header != nil {
		in, out := &in.Header, &out.Header
		*out = new(Header)
		**out = **in
	}
	if in.Cookie != nil {
		in, out := &in.Cookie, &out.Cookie
		*out = new(v1alpha1.Cookie)
		(*in).DeepCopyInto(*out)
	}
	if in.TableSize != nil {
		in, out := &in.TableSize, &out.TableSize
		*out = new(uint64)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConsistentHash.
func (in *ConsistentHash) DeepCopy() *ConsistentHash {
	if in == nil {
		return nil
	}
	out := new(ConsistentHash)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CookieBasedSessionPersistence) DeepCopyInto(out *CookieBasedSessionPersistence) {
	*out = *in
	if in.TTL != nil {
		in, out := &in.TTL, &out.TTL
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CookieBasedSessionPersistence.
func (in *CookieBasedSessionPersistence) DeepCopy() *CookieBasedSessionPersistence {
	if in == nil {
		return nil
	}
	out := new(CookieBasedSessionPersistence)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CoreListenerDetails) DeepCopyInto(out *CoreListenerDetails) {
	*out = *in
	if in.ExtensionRefs != nil {
		in, out := &in.ExtensionRefs, &out.ExtensionRefs
		*out = make([]*UnstructuredRef, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(UnstructuredRef)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = new(ResourceMetadata)
		(*in).DeepCopyInto(*out)
	}
	if in.IPFamily != nil {
		in, out := &in.IPFamily, &out.IPFamily
		*out = new(v1alpha1.IPFamily)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CoreListenerDetails.
func (in *CoreListenerDetails) DeepCopy() *CoreListenerDetails {
	if in == nil {
		return nil
	}
	out := new(CoreListenerDetails)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CredentialInjection) DeepCopyInto(out *CredentialInjection) {
	*out = *in
	if in.Header != nil {
		in, out := &in.Header, &out.Header
		*out = new(string)
		**out = **in
	}
	if in.Overwrite != nil {
		in, out := &in.Overwrite, &out.Overwrite
		*out = new(bool)
		**out = **in
	}
	if in.Credential != nil {
		in, out := &in.Credential, &out.Credential
		*out = make(PrivateBytes, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CredentialInjection.
func (in *CredentialInjection) DeepCopy() *CredentialInjection {
	if in == nil {
		return nil
	}
	out := new(CredentialInjection)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomResponse) DeepCopyInto(out *CustomResponse) {
	*out = *in
	if in.ContentType != nil {
		in, out := &in.ContentType, &out.ContentType
		*out = new(string)
		**out = **in
	}
	if in.Body != nil {
		in, out := &in.Body, &out.Body
		*out = new(string)
		**out = **in
	}
	if in.StatusCode != nil {
		in, out := &in.StatusCode, &out.StatusCode
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomResponse.
func (in *CustomResponse) DeepCopy() *CustomResponse {
	if in == nil {
		return nil
	}
	out := new(CustomResponse)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomResponseMatch) DeepCopyInto(out *CustomResponseMatch) {
	*out = *in
	if in.StatusCodes != nil {
		in, out := &in.StatusCodes, &out.StatusCodes
		*out = make([]StatusCodeMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomResponseMatch.
func (in *CustomResponseMatch) DeepCopy() *CustomResponseMatch {
	if in == nil {
		return nil
	}
	out := new(CustomResponseMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DNS) DeepCopyInto(out *DNS) {
	*out = *in
	if in.DNSRefreshRate != nil {
		in, out := &in.DNSRefreshRate, &out.DNSRefreshRate
		*out = new(v1.Duration)
		**out = **in
	}
	if in.RespectDNSTTL != nil {
		in, out := &in.RespectDNSTTL, &out.RespectDNSTTL
		*out = new(bool)
		**out = **in
	}
	if in.LookupFamily != nil {
		in, out := &in.LookupFamily, &out.LookupFamily
		*out = new(v1alpha1.DNSLookupFamily)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DNS.
func (in *DNS) DeepCopy() *DNS {
	if in == nil {
		return nil
	}
	out := new(DNS)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DestinationEndpoint) DeepCopyInto(out *DestinationEndpoint) {
	*out = *in
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(string)
		**out = **in
	}
	if in.Zone != nil {
		in, out := &in.Zone, &out.Zone
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DestinationEndpoint.
func (in *DestinationEndpoint) DeepCopy() *DestinationEndpoint {
	if in == nil {
		return nil
	}
	out := new(DestinationEndpoint)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DestinationFilters) DeepCopyInto(out *DestinationFilters) {
	*out = *in
	if in.AddRequestHeaders != nil {
		in, out := &in.AddRequestHeaders, &out.AddRequestHeaders
		*out = make([]AddHeader, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.RemoveRequestHeaders != nil {
		in, out := &in.RemoveRequestHeaders, &out.RemoveRequestHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AddResponseHeaders != nil {
		in, out := &in.AddResponseHeaders, &out.AddResponseHeaders
		*out = make([]AddHeader, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.RemoveResponseHeaders != nil {
		in, out := &in.RemoveResponseHeaders, &out.RemoveResponseHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.CredentialInjection != nil {
		in, out := &in.CredentialInjection, &out.CredentialInjection
		*out = new(CredentialInjection)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DestinationFilters.
func (in *DestinationFilters) DeepCopy() *DestinationFilters {
	if in == nil {
		return nil
	}
	out := new(DestinationFilters)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DestinationSetting) DeepCopyInto(out *DestinationSetting) {
	*out = *in
	if in.Weight != nil {
		in, out := &in.Weight, &out.Weight
		*out = new(uint32)
		**out = **in
	}
	if in.Priority != nil {
		in, out := &in.Priority, &out.Priority
		*out = new(uint32)
		**out = **in
	}
	if in.Endpoints != nil {
		in, out := &in.Endpoints, &out.Endpoints
		*out = make([]*DestinationEndpoint, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(DestinationEndpoint)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.AddressType != nil {
		in, out := &in.AddressType, &out.AddressType
		*out = new(DestinationAddressType)
		**out = **in
	}
	if in.IPFamily != nil {
		in, out := &in.IPFamily, &out.IPFamily
		*out = new(v1alpha1.IPFamily)
		**out = **in
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(TLSUpstreamConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.Filters != nil {
		in, out := &in.Filters, &out.Filters
		*out = new(DestinationFilters)
		(*in).DeepCopyInto(*out)
	}
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = new(ResourceMetadata)
		(*in).DeepCopyInto(*out)
	}
	if in.UnstructuredRef != nil {
		in, out := &in.UnstructuredRef, &out.UnstructuredRef
		*out = new(UnstructuredRef)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DestinationSetting.
func (in *DestinationSetting) DeepCopy() *DestinationSetting {
	if in == nil {
		return nil
	}
	out := new(DestinationSetting)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyExtensionFeatures) DeepCopyInto(out *EnvoyExtensionFeatures) {
	*out = *in
	if in.ExtProcs != nil {
		in, out := &in.ExtProcs, &out.ExtProcs
		*out = make([]ExtProc, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Wasms != nil {
		in, out := &in.Wasms, &out.Wasms
		*out = make([]Wasm, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Luas != nil {
		in, out := &in.Luas, &out.Luas
		*out = make([]Lua, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyExtensionFeatures.
func (in *EnvoyExtensionFeatures) DeepCopy() *EnvoyExtensionFeatures {
	if in == nil {
		return nil
	}
	out := new(EnvoyExtensionFeatures)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyPatchPolicy) DeepCopyInto(out *EnvoyPatchPolicy) {
	*out = *in
	in.EnvoyPatchPolicyStatus.DeepCopyInto(&out.EnvoyPatchPolicyStatus)
	if in.JSONPatches != nil {
		in, out := &in.JSONPatches, &out.JSONPatches
		*out = make([]*JSONPatchConfig, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(JSONPatchConfig)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyPatchPolicy.
func (in *EnvoyPatchPolicy) DeepCopy() *EnvoyPatchPolicy {
	if in == nil {
		return nil
	}
	out := new(EnvoyPatchPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EnvoyPatchPolicyStatus) DeepCopyInto(out *EnvoyPatchPolicyStatus) {
	*out = *in
	if in.Status != nil {
		in, out := &in.Status, &out.Status
		*out = new(v1alpha2.PolicyStatus)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EnvoyPatchPolicyStatus.
func (in *EnvoyPatchPolicyStatus) DeepCopy() *EnvoyPatchPolicyStatus {
	if in == nil {
		return nil
	}
	out := new(EnvoyPatchPolicyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtAuth) DeepCopyInto(out *ExtAuth) {
	*out = *in
	if in.GRPC != nil {
		in, out := &in.GRPC, &out.GRPC
		*out = new(GRPCExtAuthService)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(HTTPExtAuthService)
		(*in).DeepCopyInto(*out)
	}
	if in.Traffic != nil {
		in, out := &in.Traffic, &out.Traffic
		*out = new(TrafficFeatures)
		(*in).DeepCopyInto(*out)
	}
	if in.HeadersToExtAuth != nil {
		in, out := &in.HeadersToExtAuth, &out.HeadersToExtAuth
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.BodyToExtAuth != nil {
		in, out := &in.BodyToExtAuth, &out.BodyToExtAuth
		*out = new(BodyToExtAuth)
		**out = **in
	}
	if in.FailOpen != nil {
		in, out := &in.FailOpen, &out.FailOpen
		*out = new(bool)
		**out = **in
	}
	if in.RecomputeRoute != nil {
		in, out := &in.RecomputeRoute, &out.RecomputeRoute
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtAuth.
func (in *ExtAuth) DeepCopy() *ExtAuth {
	if in == nil {
		return nil
	}
	out := new(ExtAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtProc) DeepCopyInto(out *ExtProc) {
	*out = *in
	in.Destination.DeepCopyInto(&out.Destination)
	if in.Traffic != nil {
		in, out := &in.Traffic, &out.Traffic
		*out = new(TrafficFeatures)
		(*in).DeepCopyInto(*out)
	}
	if in.MessageTimeout != nil {
		in, out := &in.MessageTimeout, &out.MessageTimeout
		*out = new(v1.Duration)
		**out = **in
	}
	if in.FailOpen != nil {
		in, out := &in.FailOpen, &out.FailOpen
		*out = new(bool)
		**out = **in
	}
	if in.RequestBodyProcessingMode != nil {
		in, out := &in.RequestBodyProcessingMode, &out.RequestBodyProcessingMode
		*out = new(ExtProcBodyProcessingMode)
		**out = **in
	}
	if in.ResponseBodyProcessingMode != nil {
		in, out := &in.ResponseBodyProcessingMode, &out.ResponseBodyProcessingMode
		*out = new(ExtProcBodyProcessingMode)
		**out = **in
	}
	if in.RequestAttributes != nil {
		in, out := &in.RequestAttributes, &out.RequestAttributes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ResponseAttributes != nil {
		in, out := &in.ResponseAttributes, &out.ResponseAttributes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ForwardingMetadataNamespaces != nil {
		in, out := &in.ForwardingMetadataNamespaces, &out.ForwardingMetadataNamespaces
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ReceivingMetadataNamespaces != nil {
		in, out := &in.ReceivingMetadataNamespaces, &out.ReceivingMetadataNamespaces
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtProc.
func (in *ExtProc) DeepCopy() *ExtProc {
	if in == nil {
		return nil
	}
	out := new(ExtProc)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtendedHTTPPathModifier) DeepCopyInto(out *ExtendedHTTPPathModifier) {
	*out = *in
	in.HTTPPathModifier.DeepCopyInto(&out.HTTPPathModifier)
	if in.RegexMatchReplace != nil {
		in, out := &in.RegexMatchReplace, &out.RegexMatchReplace
		*out = new(RegexMatchReplace)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtendedHTTPPathModifier.
func (in *ExtendedHTTPPathModifier) DeepCopy() *ExtendedHTTPPathModifier {
	if in == nil {
		return nil
	}
	out := new(ExtendedHTTPPathModifier)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ExtractFrom) DeepCopyInto(out *ExtractFrom) {
	*out = *in
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Params != nil {
		in, out := &in.Params, &out.Params
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Cookies != nil {
		in, out := &in.Cookies, &out.Cookies
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ExtractFrom.
func (in *ExtractFrom) DeepCopy() *ExtractFrom {
	if in == nil {
		return nil
	}
	out := new(ExtractFrom)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FaultInjection) DeepCopyInto(out *FaultInjection) {
	*out = *in
	if in.Delay != nil {
		in, out := &in.Delay, &out.Delay
		*out = new(FaultInjectionDelay)
		(*in).DeepCopyInto(*out)
	}
	if in.Abort != nil {
		in, out := &in.Abort, &out.Abort
		*out = new(FaultInjectionAbort)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FaultInjection.
func (in *FaultInjection) DeepCopy() *FaultInjection {
	if in == nil {
		return nil
	}
	out := new(FaultInjection)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FaultInjectionAbort) DeepCopyInto(out *FaultInjectionAbort) {
	*out = *in
	if in.HTTPStatus != nil {
		in, out := &in.HTTPStatus, &out.HTTPStatus
		*out = new(int32)
		**out = **in
	}
	if in.GrpcStatus != nil {
		in, out := &in.GrpcStatus, &out.GrpcStatus
		*out = new(int32)
		**out = **in
	}
	if in.Percentage != nil {
		in, out := &in.Percentage, &out.Percentage
		*out = new(float32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FaultInjectionAbort.
func (in *FaultInjectionAbort) DeepCopy() *FaultInjectionAbort {
	if in == nil {
		return nil
	}
	out := new(FaultInjectionAbort)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FaultInjectionDelay) DeepCopyInto(out *FaultInjectionDelay) {
	*out = *in
	if in.FixedDelay != nil {
		in, out := &in.FixedDelay, &out.FixedDelay
		*out = new(v1.Duration)
		**out = **in
	}
	if in.Percentage != nil {
		in, out := &in.Percentage, &out.Percentage
		*out = new(float32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FaultInjectionDelay.
func (in *FaultInjectionDelay) DeepCopy() *FaultInjectionDelay {
	if in == nil {
		return nil
	}
	out := new(FaultInjectionDelay)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GRPCExtAuthService) DeepCopyInto(out *GRPCExtAuthService) {
	*out = *in
	in.Destination.DeepCopyInto(&out.Destination)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GRPCExtAuthService.
func (in *GRPCExtAuthService) DeepCopy() *GRPCExtAuthService {
	if in == nil {
		return nil
	}
	out := new(GRPCExtAuthService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GRPCHealthChecker) DeepCopyInto(out *GRPCHealthChecker) {
	*out = *in
	if in.Service != nil {
		in, out := &in.Service, &out.Service
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GRPCHealthChecker.
func (in *GRPCHealthChecker) DeepCopy() *GRPCHealthChecker {
	if in == nil {
		return nil
	}
	out := new(GRPCHealthChecker)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GlobalRateLimit) DeepCopyInto(out *GlobalRateLimit) {
	*out = *in
	if in.Rules != nil {
		in, out := &in.Rules, &out.Rules
		*out = make([]*RateLimitRule, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(RateLimitRule)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GlobalRateLimit.
func (in *GlobalRateLimit) DeepCopy() *GlobalRateLimit {
	if in == nil {
		return nil
	}
	out := new(GlobalRateLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GlobalResources) DeepCopyInto(out *GlobalResources) {
	*out = *in
	if in.EnvoyClientCertificate != nil {
		in, out := &in.EnvoyClientCertificate, &out.EnvoyClientCertificate
		*out = new(TLSCertificate)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GlobalResources.
func (in *GlobalResources) DeepCopy() *GlobalResources {
	if in == nil {
		return nil
	}
	out := new(GlobalResources)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTP10Settings) DeepCopyInto(out *HTTP10Settings) {
	*out = *in
	if in.DefaultHost != nil {
		in, out := &in.DefaultHost, &out.DefaultHost
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTP10Settings.
func (in *HTTP10Settings) DeepCopy() *HTTP10Settings {
	if in == nil {
		return nil
	}
	out := new(HTTP10Settings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTP1Settings) DeepCopyInto(out *HTTP1Settings) {
	*out = *in
	if in.HTTP10 != nil {
		in, out := &in.HTTP10, &out.HTTP10
		*out = new(HTTP10Settings)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTP1Settings.
func (in *HTTP1Settings) DeepCopy() *HTTP1Settings {
	if in == nil {
		return nil
	}
	out := new(HTTP1Settings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTP2Settings) DeepCopyInto(out *HTTP2Settings) {
	*out = *in
	if in.InitialStreamWindowSize != nil {
		in, out := &in.InitialStreamWindowSize, &out.InitialStreamWindowSize
		*out = new(uint32)
		**out = **in
	}
	if in.InitialConnectionWindowSize != nil {
		in, out := &in.InitialConnectionWindowSize, &out.InitialConnectionWindowSize
		*out = new(uint32)
		**out = **in
	}
	if in.MaxConcurrentStreams != nil {
		in, out := &in.MaxConcurrentStreams, &out.MaxConcurrentStreams
		*out = new(uint32)
		**out = **in
	}
	if in.ResetStreamOnError != nil {
		in, out := &in.ResetStreamOnError, &out.ResetStreamOnError
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTP2Settings.
func (in *HTTP2Settings) DeepCopy() *HTTP2Settings {
	if in == nil {
		return nil
	}
	out := new(HTTP2Settings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPClientTimeout) DeepCopyInto(out *HTTPClientTimeout) {
	*out = *in
	if in.RequestReceivedTimeout != nil {
		in, out := &in.RequestReceivedTimeout, &out.RequestReceivedTimeout
		*out = new(v1.Duration)
		**out = **in
	}
	if in.IdleTimeout != nil {
		in, out := &in.IdleTimeout, &out.IdleTimeout
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPClientTimeout.
func (in *HTTPClientTimeout) DeepCopy() *HTTPClientTimeout {
	if in == nil {
		return nil
	}
	out := new(HTTPClientTimeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPExtAuthService) DeepCopyInto(out *HTTPExtAuthService) {
	*out = *in
	in.Destination.DeepCopyInto(&out.Destination)
	if in.HeadersToBackend != nil {
		in, out := &in.HeadersToBackend, &out.HeadersToBackend
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPExtAuthService.
func (in *HTTPExtAuthService) DeepCopy() *HTTPExtAuthService {
	if in == nil {
		return nil
	}
	out := new(HTTPExtAuthService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPHealthChecker) DeepCopyInto(out *HTTPHealthChecker) {
	*out = *in
	if in.Method != nil {
		in, out := &in.Method, &out.Method
		*out = new(string)
		**out = **in
	}
	if in.ExpectedStatuses != nil {
		in, out := &in.ExpectedStatuses, &out.ExpectedStatuses
		*out = make([]HTTPStatus, len(*in))
		copy(*out, *in)
	}
	if in.ExpectedResponse != nil {
		in, out := &in.ExpectedResponse, &out.ExpectedResponse
		*out = new(HealthCheckPayload)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPHealthChecker.
func (in *HTTPHealthChecker) DeepCopy() *HTTPHealthChecker {
	if in == nil {
		return nil
	}
	out := new(HTTPHealthChecker)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPHostModifier) DeepCopyInto(out *HTTPHostModifier) {
	*out = *in
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
	if in.Header != nil {
		in, out := &in.Header, &out.Header
		*out = new(string)
		**out = **in
	}
	if in.Backend != nil {
		in, out := &in.Backend, &out.Backend
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPHostModifier.
func (in *HTTPHostModifier) DeepCopy() *HTTPHostModifier {
	if in == nil {
		return nil
	}
	out := new(HTTPHostModifier)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPListener) DeepCopyInto(out *HTTPListener) {
	*out = *in
	in.CoreListenerDetails.DeepCopyInto(&out.CoreListenerDetails)
	if in.Hostnames != nil {
		in, out := &in.Hostnames, &out.Hostnames
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(TLSConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.Routes != nil {
		in, out := &in.Routes, &out.Routes
		*out = make([]*HTTPRoute, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(HTTPRoute)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.TCPKeepalive != nil {
		in, out := &in.TCPKeepalive, &out.TCPKeepalive
		*out = new(TCPKeepalive)
		(*in).DeepCopyInto(*out)
	}
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = new(HeaderSettings)
		(*in).DeepCopyInto(*out)
	}
	if in.ClientIPDetection != nil {
		in, out := &in.ClientIPDetection, &out.ClientIPDetection
		*out = new(ClientIPDetectionSettings)
		(*in).DeepCopyInto(*out)
	}
	out.Path = in.Path
	if in.HTTP1 != nil {
		in, out := &in.HTTP1, &out.HTTP1
		*out = new(HTTP1Settings)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP2 != nil {
		in, out := &in.HTTP2, &out.HTTP2
		*out = new(HTTP2Settings)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP3 != nil {
		in, out := &in.HTTP3, &out.HTTP3
		*out = new(HTTP3Settings)
		**out = **in
	}
	if in.HealthCheck != nil {
		in, out := &in.HealthCheck, &out.HealthCheck
		*out = new(HealthCheckSettings)
		**out = **in
	}
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(ClientTimeout)
		(*in).DeepCopyInto(*out)
	}
	if in.Connection != nil {
		in, out := &in.Connection, &out.Connection
		*out = new(ClientConnection)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPListener.
func (in *HTTPListener) DeepCopy() *HTTPListener {
	if in == nil {
		return nil
	}
	out := new(HTTPListener)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPPathModifier) DeepCopyInto(out *HTTPPathModifier) {
	*out = *in
	if in.FullReplace != nil {
		in, out := &in.FullReplace, &out.FullReplace
		*out = new(string)
		**out = **in
	}
	if in.PrefixMatchReplace != nil {
		in, out := &in.PrefixMatchReplace, &out.PrefixMatchReplace
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPPathModifier.
func (in *HTTPPathModifier) DeepCopy() *HTTPPathModifier {
	if in == nil {
		return nil
	}
	out := new(HTTPPathModifier)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPRoute) DeepCopyInto(out *HTTPRoute) {
	*out = *in
	if in.PathMatch != nil {
		in, out := &in.PathMatch, &out.PathMatch
		*out = new(StringMatch)
		(*in).DeepCopyInto(*out)
	}
	if in.HeaderMatches != nil {
		in, out := &in.HeaderMatches, &out.HeaderMatches
		*out = make([]*StringMatch, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(StringMatch)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.QueryParamMatches != nil {
		in, out := &in.QueryParamMatches, &out.QueryParamMatches
		*out = make([]*StringMatch, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(StringMatch)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.AddRequestHeaders != nil {
		in, out := &in.AddRequestHeaders, &out.AddRequestHeaders
		*out = make([]AddHeader, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.RemoveRequestHeaders != nil {
		in, out := &in.RemoveRequestHeaders, &out.RemoveRequestHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AddResponseHeaders != nil {
		in, out := &in.AddResponseHeaders, &out.AddResponseHeaders
		*out = make([]AddHeader, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.RemoveResponseHeaders != nil {
		in, out := &in.RemoveResponseHeaders, &out.RemoveResponseHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.DirectResponse != nil {
		in, out := &in.DirectResponse, &out.DirectResponse
		*out = new(CustomResponse)
		(*in).DeepCopyInto(*out)
	}
	if in.Redirect != nil {
		in, out := &in.Redirect, &out.Redirect
		*out = new(Redirect)
		(*in).DeepCopyInto(*out)
	}
	if in.Mirrors != nil {
		in, out := &in.Mirrors, &out.Mirrors
		*out = make([]*MirrorPolicy, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(MirrorPolicy)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.Destination != nil {
		in, out := &in.Destination, &out.Destination
		*out = new(RouteDestination)
		(*in).DeepCopyInto(*out)
	}
	if in.URLRewrite != nil {
		in, out := &in.URLRewrite, &out.URLRewrite
		*out = new(URLRewrite)
		(*in).DeepCopyInto(*out)
	}
	if in.CredentialInjection != nil {
		in, out := &in.CredentialInjection, &out.CredentialInjection
		*out = new(CredentialInjection)
		(*in).DeepCopyInto(*out)
	}
	if in.ExtensionRefs != nil {
		in, out := &in.ExtensionRefs, &out.ExtensionRefs
		*out = make([]*UnstructuredRef, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(UnstructuredRef)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.BackendExtensionRefs != nil {
		in, out := &in.BackendExtensionRefs, &out.BackendExtensionRefs
		*out = make([]*UnstructuredRef, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(UnstructuredRef)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.Traffic != nil {
		in, out := &in.Traffic, &out.Traffic
		*out = new(TrafficFeatures)
		(*in).DeepCopyInto(*out)
	}
	if in.Security != nil {
		in, out := &in.Security, &out.Security
		*out = new(SecurityFeatures)
		(*in).DeepCopyInto(*out)
	}
	if in.EnvoyExtensions != nil {
		in, out := &in.EnvoyExtensions, &out.EnvoyExtensions
		*out = new(EnvoyExtensionFeatures)
		(*in).DeepCopyInto(*out)
	}
	if in.UseClientProtocol != nil {
		in, out := &in.UseClientProtocol, &out.UseClientProtocol
		*out = new(bool)
		**out = **in
	}
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = new(ResourceMetadata)
		(*in).DeepCopyInto(*out)
	}
	if in.SessionPersistence != nil {
		in, out := &in.SessionPersistence, &out.SessionPersistence
		*out = new(SessionPersistence)
		(*in).DeepCopyInto(*out)
	}
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(v1.Duration)
		**out = **in
	}
	if in.Retry != nil {
		in, out := &in.Retry, &out.Retry
		*out = new(Retry)
		(*in).DeepCopyInto(*out)
	}
	if in.CORS != nil {
		in, out := &in.CORS, &out.CORS
		*out = new(CORS)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPRoute.
func (in *HTTPRoute) DeepCopy() *HTTPRoute {
	if in == nil {
		return nil
	}
	out := new(HTTPRoute)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPTimeout) DeepCopyInto(out *HTTPTimeout) {
	*out = *in
	if in.RequestTimeout != nil {
		in, out := &in.RequestTimeout, &out.RequestTimeout
		*out = new(v1.Duration)
		**out = **in
	}
	if in.ConnectionIdleTimeout != nil {
		in, out := &in.ConnectionIdleTimeout, &out.ConnectionIdleTimeout
		*out = new(v1.Duration)
		**out = **in
	}
	if in.MaxConnectionDuration != nil {
		in, out := &in.MaxConnectionDuration, &out.MaxConnectionDuration
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPTimeout.
func (in *HTTPTimeout) DeepCopy() *HTTPTimeout {
	if in == nil {
		return nil
	}
	out := new(HTTPTimeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPUpgradeConfig) DeepCopyInto(out *HTTPUpgradeConfig) {
	*out = *in
	if in.Connect != nil {
		in, out := &in.Connect, &out.Connect
		*out = new(ConnectConfig)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPUpgradeConfig.
func (in *HTTPUpgradeConfig) DeepCopy() *HTTPUpgradeConfig {
	if in == nil {
		return nil
	}
	out := new(HTTPUpgradeConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HTTPWasmCode) DeepCopyInto(out *HTTPWasmCode) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HTTPWasmCode.
func (in *HTTPWasmCode) DeepCopy() *HTTPWasmCode {
	if in == nil {
		return nil
	}
	out := new(HTTPWasmCode)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HeaderBasedSessionPersistence) DeepCopyInto(out *HeaderBasedSessionPersistence) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HeaderBasedSessionPersistence.
func (in *HeaderBasedSessionPersistence) DeepCopy() *HeaderBasedSessionPersistence {
	if in == nil {
		return nil
	}
	out := new(HeaderBasedSessionPersistence)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HeaderSettings) DeepCopyInto(out *HeaderSettings) {
	*out = *in
	if in.XForwardedClientCert != nil {
		in, out := &in.XForwardedClientCert, &out.XForwardedClientCert
		*out = new(XForwardedClientCert)
		(*in).DeepCopyInto(*out)
	}
	if in.RequestID != nil {
		in, out := &in.RequestID, &out.RequestID
		*out = new(RequestIDAction)
		**out = **in
	}
	if in.EarlyAddRequestHeaders != nil {
		in, out := &in.EarlyAddRequestHeaders, &out.EarlyAddRequestHeaders
		*out = make([]AddHeader, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.EarlyRemoveRequestHeaders != nil {
		in, out := &in.EarlyRemoveRequestHeaders, &out.EarlyRemoveRequestHeaders
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HeaderSettings.
func (in *HeaderSettings) DeepCopy() *HeaderSettings {
	if in == nil {
		return nil
	}
	out := new(HeaderSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HealthCheck) DeepCopyInto(out *HealthCheck) {
	*out = *in
	if in.Active != nil {
		in, out := &in.Active, &out.Active
		*out = new(ActiveHealthCheck)
		(*in).DeepCopyInto(*out)
	}
	if in.Passive != nil {
		in, out := &in.Passive, &out.Passive
		*out = new(OutlierDetection)
		(*in).DeepCopyInto(*out)
	}
	if in.PanicThreshold != nil {
		in, out := &in.PanicThreshold, &out.PanicThreshold
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HealthCheck.
func (in *HealthCheck) DeepCopy() *HealthCheck {
	if in == nil {
		return nil
	}
	out := new(HealthCheck)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HealthCheckPayload) DeepCopyInto(out *HealthCheckPayload) {
	*out = *in
	if in.Text != nil {
		in, out := &in.Text, &out.Text
		*out = new(string)
		**out = **in
	}
	if in.Binary != nil {
		in, out := &in.Binary, &out.Binary
		*out = make([]byte, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HealthCheckPayload.
func (in *HealthCheckPayload) DeepCopy() *HealthCheckPayload {
	if in == nil {
		return nil
	}
	out := new(HealthCheckPayload)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HealthCheckSettings) DeepCopyInto(out *HealthCheckSettings) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HealthCheckSettings.
func (in *HealthCheckSettings) DeepCopy() *HealthCheckSettings {
	if in == nil {
		return nil
	}
	out := new(HealthCheckSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Infra) DeepCopyInto(out *Infra) {
	*out = *in
	if in.Proxy != nil {
		in, out := &in.Proxy, &out.Proxy
		*out = new(ProxyInfra)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Infra.
func (in *Infra) DeepCopy() *Infra {
	if in == nil {
		return nil
	}
	out := new(Infra)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *InfraMetadata) DeepCopyInto(out *InfraMetadata) {
	*out = *in
	if in.Annotations != nil {
		in, out := &in.Annotations, &out.Annotations
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Labels != nil {
		in, out := &in.Labels, &out.Labels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.OwnerReference != nil {
		in, out := &in.OwnerReference, &out.OwnerReference
		*out = new(ResourceMetadata)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new InfraMetadata.
func (in *InfraMetadata) DeepCopy() *InfraMetadata {
	if in == nil {
		return nil
	}
	out := new(InfraMetadata)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JSONAccessLog) DeepCopyInto(out *JSONAccessLog) {
	*out = *in
	if in.CELMatches != nil {
		in, out := &in.CELMatches, &out.CELMatches
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.JSON != nil {
		in, out := &in.JSON, &out.JSON
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.LogType != nil {
		in, out := &in.LogType, &out.LogType
		*out = new(ProxyAccessLogType)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JSONAccessLog.
func (in *JSONAccessLog) DeepCopy() *JSONAccessLog {
	if in == nil {
		return nil
	}
	out := new(JSONAccessLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JSONPatchConfig) DeepCopyInto(out *JSONPatchConfig) {
	*out = *in
	in.Operation.DeepCopyInto(&out.Operation)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JSONPatchConfig.
func (in *JSONPatchConfig) DeepCopy() *JSONPatchConfig {
	if in == nil {
		return nil
	}
	out := new(JSONPatchConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JSONPatchOperation) DeepCopyInto(out *JSONPatchOperation) {
	*out = *in
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(string)
		**out = **in
	}
	if in.JSONPath != nil {
		in, out := &in.JSONPath, &out.JSONPath
		*out = new(string)
		**out = **in
	}
	if in.From != nil {
		in, out := &in.From, &out.From
		*out = new(string)
		**out = **in
	}
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = new(apiextensionsv1.JSON)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JSONPatchOperation.
func (in *JSONPatchOperation) DeepCopy() *JSONPatchOperation {
	if in == nil {
		return nil
	}
	out := new(JSONPatchOperation)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JWT) DeepCopyInto(out *JWT) {
	*out = *in
	if in.Providers != nil {
		in, out := &in.Providers, &out.Providers
		*out = make([]JWTProvider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JWT.
func (in *JWT) DeepCopy() *JWT {
	if in == nil {
		return nil
	}
	out := new(JWT)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *JWTProvider) DeepCopyInto(out *JWTProvider) {
	*out = *in
	if in.Audiences != nil {
		in, out := &in.Audiences, &out.Audiences
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.RemoteJWKS != nil {
		in, out := &in.RemoteJWKS, &out.RemoteJWKS
		*out = new(RemoteJWKS)
		(*in).DeepCopyInto(*out)
	}
	if in.LocalJWKS != nil {
		in, out := &in.LocalJWKS, &out.LocalJWKS
		*out = new(string)
		**out = **in
	}
	if in.ClaimToHeaders != nil {
		in, out := &in.ClaimToHeaders, &out.ClaimToHeaders
		*out = make([]v1alpha1.ClaimToHeader, len(*in))
		copy(*out, *in)
	}
	if in.RecomputeRoute != nil {
		in, out := &in.RecomputeRoute, &out.RecomputeRoute
		*out = new(bool)
		**out = **in
	}
	if in.ExtractFrom != nil {
		in, out := &in.ExtractFrom, &out.ExtractFrom
		*out = new(v1alpha1.JWTExtractor)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new JWTProvider.
func (in *JWTProvider) DeepCopy() *JWTProvider {
	if in == nil {
		return nil
	}
	out := new(JWTProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LeastRequest) DeepCopyInto(out *LeastRequest) {
	*out = *in
	if in.SlowStart != nil {
		in, out := &in.SlowStart, &out.SlowStart
		*out = new(SlowStart)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LeastRequest.
func (in *LeastRequest) DeepCopy() *LeastRequest {
	if in == nil {
		return nil
	}
	out := new(LeastRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ListenerPort) DeepCopyInto(out *ListenerPort) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ListenerPort.
func (in *ListenerPort) DeepCopy() *ListenerPort {
	if in == nil {
		return nil
	}
	out := new(ListenerPort)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LoadBalancer) DeepCopyInto(out *LoadBalancer) {
	*out = *in
	if in.RoundRobin != nil {
		in, out := &in.RoundRobin, &out.RoundRobin
		*out = new(RoundRobin)
		(*in).DeepCopyInto(*out)
	}
	if in.LeastRequest != nil {
		in, out := &in.LeastRequest, &out.LeastRequest
		*out = new(LeastRequest)
		(*in).DeepCopyInto(*out)
	}
	if in.Random != nil {
		in, out := &in.Random, &out.Random
		*out = new(Random)
		**out = **in
	}
	if in.ConsistentHash != nil {
		in, out := &in.ConsistentHash, &out.ConsistentHash
		*out = new(ConsistentHash)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LoadBalancer.
func (in *LoadBalancer) DeepCopy() *LoadBalancer {
	if in == nil {
		return nil
	}
	out := new(LoadBalancer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LocalRateLimit) DeepCopyInto(out *LocalRateLimit) {
	*out = *in
	out.Default = in.Default
	if in.Rules != nil {
		in, out := &in.Rules, &out.Rules
		*out = make([]*RateLimitRule, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(RateLimitRule)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LocalRateLimit.
func (in *LocalRateLimit) DeepCopy() *LocalRateLimit {
	if in == nil {
		return nil
	}
	out := new(LocalRateLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Lua) DeepCopyInto(out *Lua) {
	*out = *in
	if in.Code != nil {
		in, out := &in.Code, &out.Code
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Lua.
func (in *Lua) DeepCopy() *Lua {
	if in == nil {
		return nil
	}
	out := new(Lua)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Metrics) DeepCopyInto(out *Metrics) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Metrics.
func (in *Metrics) DeepCopy() *Metrics {
	if in == nil {
		return nil
	}
	out := new(Metrics)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MirrorPolicy) DeepCopyInto(out *MirrorPolicy) {
	*out = *in
	if in.Destination != nil {
		in, out := &in.Destination, &out.Destination
		*out = new(RouteDestination)
		(*in).DeepCopyInto(*out)
	}
	if in.Percentage != nil {
		in, out := &in.Percentage, &out.Percentage
		*out = new(float32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MirrorPolicy.
func (in *MirrorPolicy) DeepCopy() *MirrorPolicy {
	if in == nil {
		return nil
	}
	out := new(MirrorPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OIDC) DeepCopyInto(out *OIDC) {
	*out = *in
	in.Provider.DeepCopyInto(&out.Provider)
	if in.ClientSecret != nil {
		in, out := &in.ClientSecret, &out.ClientSecret
		*out = make(PrivateBytes, len(*in))
		copy(*out, *in)
	}
	if in.HMACSecret != nil {
		in, out := &in.HMACSecret, &out.HMACSecret
		*out = make(PrivateBytes, len(*in))
		copy(*out, *in)
	}
	if in.Scopes != nil {
		in, out := &in.Scopes, &out.Scopes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.DefaultTokenTTL != nil {
		in, out := &in.DefaultTokenTTL, &out.DefaultTokenTTL
		*out = new(v1.Duration)
		**out = **in
	}
	if in.DefaultRefreshTokenTTL != nil {
		in, out := &in.DefaultRefreshTokenTTL, &out.DefaultRefreshTokenTTL
		*out = new(v1.Duration)
		**out = **in
	}
	if in.CookieNameOverrides != nil {
		in, out := &in.CookieNameOverrides, &out.CookieNameOverrides
		*out = new(v1alpha1.OIDCCookieNames)
		(*in).DeepCopyInto(*out)
	}
	if in.CookieDomain != nil {
		in, out := &in.CookieDomain, &out.CookieDomain
		*out = new(string)
		**out = **in
	}
	if in.DenyRedirect != nil {
		in, out := &in.DenyRedirect, &out.DenyRedirect
		*out = new(v1alpha1.OIDCDenyRedirect)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OIDC.
func (in *OIDC) DeepCopy() *OIDC {
	if in == nil {
		return nil
	}
	out := new(OIDC)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OIDCProvider) DeepCopyInto(out *OIDCProvider) {
	*out = *in
	if in.Destination != nil {
		in, out := &in.Destination, &out.Destination
		*out = new(RouteDestination)
		(*in).DeepCopyInto(*out)
	}
	if in.Traffic != nil {
		in, out := &in.Traffic, &out.Traffic
		*out = new(TrafficFeatures)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OIDCProvider.
func (in *OIDCProvider) DeepCopy() *OIDCProvider {
	if in == nil {
		return nil
	}
	out := new(OIDCProvider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OpenTelemetryAccessLog) DeepCopyInto(out *OpenTelemetryAccessLog) {
	*out = *in
	if in.CELMatches != nil {
		in, out := &in.CELMatches, &out.CELMatches
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Text != nil {
		in, out := &in.Text, &out.Text
		*out = new(string)
		**out = **in
	}
	if in.Attributes != nil {
		in, out := &in.Attributes, &out.Attributes
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	in.Destination.DeepCopyInto(&out.Destination)
	if in.Traffic != nil {
		in, out := &in.Traffic, &out.Traffic
		*out = new(TrafficFeatures)
		(*in).DeepCopyInto(*out)
	}
	if in.LogType != nil {
		in, out := &in.LogType, &out.LogType
		*out = new(ProxyAccessLogType)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OpenTelemetryAccessLog.
func (in *OpenTelemetryAccessLog) DeepCopy() *OpenTelemetryAccessLog {
	if in == nil {
		return nil
	}
	out := new(OpenTelemetryAccessLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OutlierDetection) DeepCopyInto(out *OutlierDetection) {
	*out = *in
	if in.Interval != nil {
		in, out := &in.Interval, &out.Interval
		*out = new(v1.Duration)
		**out = **in
	}
	if in.SplitExternalLocalOriginErrors != nil {
		in, out := &in.SplitExternalLocalOriginErrors, &out.SplitExternalLocalOriginErrors
		*out = new(bool)
		**out = **in
	}
	if in.ConsecutiveLocalOriginFailures != nil {
		in, out := &in.ConsecutiveLocalOriginFailures, &out.ConsecutiveLocalOriginFailures
		*out = new(uint32)
		**out = **in
	}
	if in.ConsecutiveGatewayErrors != nil {
		in, out := &in.ConsecutiveGatewayErrors, &out.ConsecutiveGatewayErrors
		*out = new(uint32)
		**out = **in
	}
	if in.Consecutive5xxErrors != nil {
		in, out := &in.Consecutive5xxErrors, &out.Consecutive5xxErrors
		*out = new(uint32)
		**out = **in
	}
	if in.BaseEjectionTime != nil {
		in, out := &in.BaseEjectionTime, &out.BaseEjectionTime
		*out = new(v1.Duration)
		**out = **in
	}
	if in.MaxEjectionPercent != nil {
		in, out := &in.MaxEjectionPercent, &out.MaxEjectionPercent
		*out = new(int32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OutlierDetection.
func (in *OutlierDetection) DeepCopy() *OutlierDetection {
	if in == nil {
		return nil
	}
	out := new(OutlierDetection)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PathSettings) DeepCopyInto(out *PathSettings) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PathSettings.
func (in *PathSettings) DeepCopy() *PathSettings {
	if in == nil {
		return nil
	}
	out := new(PathSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PerEndpointCircuitBreakers) DeepCopyInto(out *PerEndpointCircuitBreakers) {
	*out = *in
	if in.MaxConnections != nil {
		in, out := &in.MaxConnections, &out.MaxConnections
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PerEndpointCircuitBreakers.
func (in *PerEndpointCircuitBreakers) DeepCopy() *PerEndpointCircuitBreakers {
	if in == nil {
		return nil
	}
	out := new(PerEndpointCircuitBreakers)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PerRetryPolicy) DeepCopyInto(out *PerRetryPolicy) {
	*out = *in
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(v1.Duration)
		**out = **in
	}
	if in.BackOff != nil {
		in, out := &in.BackOff, &out.BackOff
		*out = new(BackOffPolicy)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PerRetryPolicy.
func (in *PerRetryPolicy) DeepCopy() *PerRetryPolicy {
	if in == nil {
		return nil
	}
	out := new(PerRetryPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Principal) DeepCopyInto(out *Principal) {
	*out = *in
	if in.ClientCIDRs != nil {
		in, out := &in.ClientCIDRs, &out.ClientCIDRs
		*out = make([]*CIDRMatch, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(CIDRMatch)
				**out = **in
			}
		}
	}
	if in.JWT != nil {
		in, out := &in.JWT, &out.JWT
		*out = new(v1alpha1.JWTPrincipal)
		(*in).DeepCopyInto(*out)
	}
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make([]v1alpha1.AuthorizationHeaderMatch, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Principal.
func (in *Principal) DeepCopy() *Principal {
	if in == nil {
		return nil
	}
	out := new(Principal)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyInfra) DeepCopyInto(out *ProxyInfra) {
	*out = *in
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = new(InfraMetadata)
		(*in).DeepCopyInto(*out)
	}
	if in.Config != nil {
		in, out := &in.Config, &out.Config
		*out = new(v1alpha1.EnvoyProxy)
		(*in).DeepCopyInto(*out)
	}
	if in.Listeners != nil {
		in, out := &in.Listeners, &out.Listeners
		*out = make([]*ProxyListener, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(ProxyListener)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.Addresses != nil {
		in, out := &in.Addresses, &out.Addresses
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyInfra.
func (in *ProxyInfra) DeepCopy() *ProxyInfra {
	if in == nil {
		return nil
	}
	out := new(ProxyInfra)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyListener) DeepCopyInto(out *ProxyListener) {
	*out = *in
	if in.Address != nil {
		in, out := &in.Address, &out.Address
		*out = new(string)
		**out = **in
	}
	if in.Ports != nil {
		in, out := &in.Ports, &out.Ports
		*out = make([]ListenerPort, len(*in))
		copy(*out, *in)
	}
	if in.HTTP3 != nil {
		in, out := &in.HTTP3, &out.HTTP3
		*out = new(HTTP3Settings)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyListener.
func (in *ProxyListener) DeepCopy() *ProxyListener {
	if in == nil {
		return nil
	}
	out := new(ProxyListener)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProxyProtocol) DeepCopyInto(out *ProxyProtocol) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProxyProtocol.
func (in *ProxyProtocol) DeepCopy() *ProxyProtocol {
	if in == nil {
		return nil
	}
	out := new(ProxyProtocol)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Random) DeepCopyInto(out *Random) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Random.
func (in *Random) DeepCopy() *Random {
	if in == nil {
		return nil
	}
	out := new(Random)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimit) DeepCopyInto(out *RateLimit) {
	*out = *in
	if in.Global != nil {
		in, out := &in.Global, &out.Global
		*out = new(GlobalRateLimit)
		(*in).DeepCopyInto(*out)
	}
	if in.Local != nil {
		in, out := &in.Local, &out.Local
		*out = new(LocalRateLimit)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimit.
func (in *RateLimit) DeepCopy() *RateLimit {
	if in == nil {
		return nil
	}
	out := new(RateLimit)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitCost) DeepCopyInto(out *RateLimitCost) {
	*out = *in
	if in.Number != nil {
		in, out := &in.Number, &out.Number
		*out = new(uint64)
		**out = **in
	}
	if in.Format != nil {
		in, out := &in.Format, &out.Format
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitCost.
func (in *RateLimitCost) DeepCopy() *RateLimitCost {
	if in == nil {
		return nil
	}
	out := new(RateLimitCost)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitRule) DeepCopyInto(out *RateLimitRule) {
	*out = *in
	if in.HeaderMatches != nil {
		in, out := &in.HeaderMatches, &out.HeaderMatches
		*out = make([]*StringMatch, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(StringMatch)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.CIDRMatch != nil {
		in, out := &in.CIDRMatch, &out.CIDRMatch
		*out = new(CIDRMatch)
		**out = **in
	}
	out.Limit = in.Limit
	if in.RequestCost != nil {
		in, out := &in.RequestCost, &out.RequestCost
		*out = new(RateLimitCost)
		(*in).DeepCopyInto(*out)
	}
	if in.ResponseCost != nil {
		in, out := &in.ResponseCost, &out.ResponseCost
		*out = new(RateLimitCost)
		(*in).DeepCopyInto(*out)
	}
	if in.Shared != nil {
		in, out := &in.Shared, &out.Shared
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitRule.
func (in *RateLimitRule) DeepCopy() *RateLimitRule {
	if in == nil {
		return nil
	}
	out := new(RateLimitRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RateLimitValue) DeepCopyInto(out *RateLimitValue) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RateLimitValue.
func (in *RateLimitValue) DeepCopy() *RateLimitValue {
	if in == nil {
		return nil
	}
	out := new(RateLimitValue)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ReadyListener) DeepCopyInto(out *ReadyListener) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ReadyListener.
func (in *ReadyListener) DeepCopy() *ReadyListener {
	if in == nil {
		return nil
	}
	out := new(ReadyListener)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Redirect) DeepCopyInto(out *Redirect) {
	*out = *in
	if in.Scheme != nil {
		in, out := &in.Scheme, &out.Scheme
		*out = new(string)
		**out = **in
	}
	if in.Hostname != nil {
		in, out := &in.Hostname, &out.Hostname
		*out = new(string)
		**out = **in
	}
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(HTTPPathModifier)
		(*in).DeepCopyInto(*out)
	}
	if in.Port != nil {
		in, out := &in.Port, &out.Port
		*out = new(uint32)
		**out = **in
	}
	if in.StatusCode != nil {
		in, out := &in.StatusCode, &out.StatusCode
		*out = new(int32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Redirect.
func (in *Redirect) DeepCopy() *Redirect {
	if in == nil {
		return nil
	}
	out := new(Redirect)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RegexMatchReplace) DeepCopyInto(out *RegexMatchReplace) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RegexMatchReplace.
func (in *RegexMatchReplace) DeepCopy() *RegexMatchReplace {
	if in == nil {
		return nil
	}
	out := new(RegexMatchReplace)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RemoteJWKS) DeepCopyInto(out *RemoteJWKS) {
	*out = *in
	if in.Destination != nil {
		in, out := &in.Destination, &out.Destination
		*out = new(RouteDestination)
		(*in).DeepCopyInto(*out)
	}
	if in.Traffic != nil {
		in, out := &in.Traffic, &out.Traffic
		*out = new(TrafficFeatures)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RemoteJWKS.
func (in *RemoteJWKS) DeepCopy() *RemoteJWKS {
	if in == nil {
		return nil
	}
	out := new(RemoteJWKS)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RequestBuffer) DeepCopyInto(out *RequestBuffer) {
	*out = *in
	out.Limit = in.Limit.DeepCopy()
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RequestBuffer.
func (in *RequestBuffer) DeepCopy() *RequestBuffer {
	if in == nil {
		return nil
	}
	out := new(RequestBuffer)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ResourceMetadata) DeepCopyInto(out *ResourceMetadata) {
	*out = *in
	if in.Annotations != nil {
		in, out := &in.Annotations, &out.Annotations
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ResourceMetadata.
func (in *ResourceMetadata) DeepCopy() *ResourceMetadata {
	if in == nil {
		return nil
	}
	out := new(ResourceMetadata)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ResponseOverride) DeepCopyInto(out *ResponseOverride) {
	*out = *in
	if in.Rules != nil {
		in, out := &in.Rules, &out.Rules
		*out = make([]ResponseOverrideRule, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ResponseOverride.
func (in *ResponseOverride) DeepCopy() *ResponseOverride {
	if in == nil {
		return nil
	}
	out := new(ResponseOverride)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ResponseOverrideRule) DeepCopyInto(out *ResponseOverrideRule) {
	*out = *in
	in.Match.DeepCopyInto(&out.Match)
	if in.Response != nil {
		in, out := &in.Response, &out.Response
		*out = new(CustomResponse)
		(*in).DeepCopyInto(*out)
	}
	if in.Redirect != nil {
		in, out := &in.Redirect, &out.Redirect
		*out = new(Redirect)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ResponseOverrideRule.
func (in *ResponseOverrideRule) DeepCopy() *ResponseOverrideRule {
	if in == nil {
		return nil
	}
	out := new(ResponseOverrideRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Retry) DeepCopyInto(out *Retry) {
	*out = *in
	if in.NumRetries != nil {
		in, out := &in.NumRetries, &out.NumRetries
		*out = new(uint32)
		**out = **in
	}
	if in.NumAttemptsPerPriority != nil {
		in, out := &in.NumAttemptsPerPriority, &out.NumAttemptsPerPriority
		*out = new(int32)
		**out = **in
	}
	if in.RetryOn != nil {
		in, out := &in.RetryOn, &out.RetryOn
		*out = new(RetryOn)
		(*in).DeepCopyInto(*out)
	}
	if in.PerRetry != nil {
		in, out := &in.PerRetry, &out.PerRetry
		*out = new(PerRetryPolicy)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Retry.
func (in *Retry) DeepCopy() *Retry {
	if in == nil {
		return nil
	}
	out := new(Retry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RetryOn) DeepCopyInto(out *RetryOn) {
	*out = *in
	if in.Triggers != nil {
		in, out := &in.Triggers, &out.Triggers
		*out = make([]TriggerEnum, len(*in))
		copy(*out, *in)
	}
	if in.HTTPStatusCodes != nil {
		in, out := &in.HTTPStatusCodes, &out.HTTPStatusCodes
		*out = make([]HTTPStatus, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RetryOn.
func (in *RetryOn) DeepCopy() *RetryOn {
	if in == nil {
		return nil
	}
	out := new(RetryOn)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoundRobin) DeepCopyInto(out *RoundRobin) {
	*out = *in
	if in.SlowStart != nil {
		in, out := &in.SlowStart, &out.SlowStart
		*out = new(SlowStart)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoundRobin.
func (in *RoundRobin) DeepCopy() *RoundRobin {
	if in == nil {
		return nil
	}
	out := new(RoundRobin)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RouteDestination) DeepCopyInto(out *RouteDestination) {
	*out = *in
	if in.StatName != nil {
		in, out := &in.StatName, &out.StatName
		*out = new(string)
		**out = **in
	}
	if in.Settings != nil {
		in, out := &in.Settings, &out.Settings
		*out = make([]*DestinationSetting, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(DestinationSetting)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.Metadata != nil {
		in, out := &in.Metadata, &out.Metadata
		*out = new(ResourceMetadata)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RouteDestination.
func (in *RouteDestination) DeepCopy() *RouteDestination {
	if in == nil {
		return nil
	}
	out := new(RouteDestination)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SecurityFeatures) DeepCopyInto(out *SecurityFeatures) {
	*out = *in
	if in.CORS != nil {
		in, out := &in.CORS, &out.CORS
		*out = new(CORS)
		(*in).DeepCopyInto(*out)
	}
	if in.JWT != nil {
		in, out := &in.JWT, &out.JWT
		*out = new(JWT)
		(*in).DeepCopyInto(*out)
	}
	if in.OIDC != nil {
		in, out := &in.OIDC, &out.OIDC
		*out = new(OIDC)
		(*in).DeepCopyInto(*out)
	}
	if in.APIKeyAuth != nil {
		in, out := &in.APIKeyAuth, &out.APIKeyAuth
		*out = new(APIKeyAuth)
		(*in).DeepCopyInto(*out)
	}
	if in.BasicAuth != nil {
		in, out := &in.BasicAuth, &out.BasicAuth
		*out = new(BasicAuth)
		(*in).DeepCopyInto(*out)
	}
	if in.ExtAuth != nil {
		in, out := &in.ExtAuth, &out.ExtAuth
		*out = new(ExtAuth)
		(*in).DeepCopyInto(*out)
	}
	if in.Authorization != nil {
		in, out := &in.Authorization, &out.Authorization
		*out = new(Authorization)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SecurityFeatures.
func (in *SecurityFeatures) DeepCopy() *SecurityFeatures {
	if in == nil {
		return nil
	}
	out := new(SecurityFeatures)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SessionPersistence) DeepCopyInto(out *SessionPersistence) {
	*out = *in
	if in.Cookie != nil {
		in, out := &in.Cookie, &out.Cookie
		*out = new(CookieBasedSessionPersistence)
		(*in).DeepCopyInto(*out)
	}
	if in.Header != nil {
		in, out := &in.Header, &out.Header
		*out = new(HeaderBasedSessionPersistence)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SessionPersistence.
func (in *SessionPersistence) DeepCopy() *SessionPersistence {
	if in == nil {
		return nil
	}
	out := new(SessionPersistence)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SlowStart) DeepCopyInto(out *SlowStart) {
	*out = *in
	if in.Window != nil {
		in, out := &in.Window, &out.Window
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SlowStart.
func (in *SlowStart) DeepCopy() *SlowStart {
	if in == nil {
		return nil
	}
	out := new(SlowStart)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StatusCodeMatch) DeepCopyInto(out *StatusCodeMatch) {
	*out = *in
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = new(int)
		**out = **in
	}
	if in.Range != nil {
		in, out := &in.Range, &out.Range
		*out = new(StatusCodeRange)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StatusCodeMatch.
func (in *StatusCodeMatch) DeepCopy() *StatusCodeMatch {
	if in == nil {
		return nil
	}
	out := new(StatusCodeMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StringMatch) DeepCopyInto(out *StringMatch) {
	*out = *in
	if in.Exact != nil {
		in, out := &in.Exact, &out.Exact
		*out = new(string)
		**out = **in
	}
	if in.Prefix != nil {
		in, out := &in.Prefix, &out.Prefix
		*out = new(string)
		**out = **in
	}
	if in.Suffix != nil {
		in, out := &in.Suffix, &out.Suffix
		*out = new(string)
		**out = **in
	}
	if in.SafeRegex != nil {
		in, out := &in.SafeRegex, &out.SafeRegex
		*out = new(string)
		**out = **in
	}
	if in.Invert != nil {
		in, out := &in.Invert, &out.Invert
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StringMatch.
func (in *StringMatch) DeepCopy() *StringMatch {
	if in == nil {
		return nil
	}
	out := new(StringMatch)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SubjectAltName) DeepCopyInto(out *SubjectAltName) {
	*out = *in
	if in.Hostname != nil {
		in, out := &in.Hostname, &out.Hostname
		*out = new(string)
		**out = **in
	}
	if in.URI != nil {
		in, out := &in.URI, &out.URI
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SubjectAltName.
func (in *SubjectAltName) DeepCopy() *SubjectAltName {
	if in == nil {
		return nil
	}
	out := new(SubjectAltName)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPClientTimeout) DeepCopyInto(out *TCPClientTimeout) {
	*out = *in
	if in.IdleTimeout != nil {
		in, out := &in.IdleTimeout, &out.IdleTimeout
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPClientTimeout.
func (in *TCPClientTimeout) DeepCopy() *TCPClientTimeout {
	if in == nil {
		return nil
	}
	out := new(TCPClientTimeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPHealthChecker) DeepCopyInto(out *TCPHealthChecker) {
	*out = *in
	if in.Send != nil {
		in, out := &in.Send, &out.Send
		*out = new(HealthCheckPayload)
		(*in).DeepCopyInto(*out)
	}
	if in.Receive != nil {
		in, out := &in.Receive, &out.Receive
		*out = new(HealthCheckPayload)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPHealthChecker.
func (in *TCPHealthChecker) DeepCopy() *TCPHealthChecker {
	if in == nil {
		return nil
	}
	out := new(TCPHealthChecker)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPKeepalive) DeepCopyInto(out *TCPKeepalive) {
	*out = *in
	if in.Probes != nil {
		in, out := &in.Probes, &out.Probes
		*out = new(uint32)
		**out = **in
	}
	if in.IdleTime != nil {
		in, out := &in.IdleTime, &out.IdleTime
		*out = new(uint32)
		**out = **in
	}
	if in.Interval != nil {
		in, out := &in.Interval, &out.Interval
		*out = new(uint32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPKeepalive.
func (in *TCPKeepalive) DeepCopy() *TCPKeepalive {
	if in == nil {
		return nil
	}
	out := new(TCPKeepalive)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPListener) DeepCopyInto(out *TCPListener) {
	*out = *in
	in.CoreListenerDetails.DeepCopyInto(&out.CoreListenerDetails)
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(TLSConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.TCPKeepalive != nil {
		in, out := &in.TCPKeepalive, &out.TCPKeepalive
		*out = new(TCPKeepalive)
		(*in).DeepCopyInto(*out)
	}
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(ClientTimeout)
		(*in).DeepCopyInto(*out)
	}
	if in.Connection != nil {
		in, out := &in.Connection, &out.Connection
		*out = new(ClientConnection)
		(*in).DeepCopyInto(*out)
	}
	if in.Routes != nil {
		in, out := &in.Routes, &out.Routes
		*out = make([]*TCPRoute, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(TCPRoute)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPListener.
func (in *TCPListener) DeepCopy() *TCPListener {
	if in == nil {
		return nil
	}
	out := new(TCPListener)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPRoute) DeepCopyInto(out *TCPRoute) {
	*out = *in
	if in.TLS != nil {
		in, out := &in.TLS, &out.TLS
		*out = new(TLS)
		(*in).DeepCopyInto(*out)
	}
	if in.Destination != nil {
		in, out := &in.Destination, &out.Destination
		*out = new(RouteDestination)
		(*in).DeepCopyInto(*out)
	}
	if in.TCPKeepalive != nil {
		in, out := &in.TCPKeepalive, &out.TCPKeepalive
		*out = new(TCPKeepalive)
		(*in).DeepCopyInto(*out)
	}
	if in.LoadBalancer != nil {
		in, out := &in.LoadBalancer, &out.LoadBalancer
		*out = new(LoadBalancer)
		(*in).DeepCopyInto(*out)
	}
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(Timeout)
		(*in).DeepCopyInto(*out)
	}
	if in.CircuitBreaker != nil {
		in, out := &in.CircuitBreaker, &out.CircuitBreaker
		*out = new(CircuitBreaker)
		(*in).DeepCopyInto(*out)
	}
	if in.HealthCheck != nil {
		in, out := &in.HealthCheck, &out.HealthCheck
		*out = new(HealthCheck)
		(*in).DeepCopyInto(*out)
	}
	if in.ProxyProtocol != nil {
		in, out := &in.ProxyProtocol, &out.ProxyProtocol
		*out = new(ProxyProtocol)
		**out = **in
	}
	if in.BackendConnection != nil {
		in, out := &in.BackendConnection, &out.BackendConnection
		*out = new(BackendConnection)
		(*in).DeepCopyInto(*out)
	}
	if in.DNS != nil {
		in, out := &in.DNS, &out.DNS
		*out = new(DNS)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPRoute.
func (in *TCPRoute) DeepCopy() *TCPRoute {
	if in == nil {
		return nil
	}
	out := new(TCPRoute)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TCPTimeout) DeepCopyInto(out *TCPTimeout) {
	*out = *in
	if in.ConnectTimeout != nil {
		in, out := &in.ConnectTimeout, &out.ConnectTimeout
		*out = new(v1.Duration)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TCPTimeout.
func (in *TCPTimeout) DeepCopy() *TCPTimeout {
	if in == nil {
		return nil
	}
	out := new(TCPTimeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLS) DeepCopyInto(out *TLS) {
	*out = *in
	if in.TLSInspectorConfig != nil {
		in, out := &in.TLSInspectorConfig, &out.TLSInspectorConfig
		*out = new(TLSInspectorConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.Terminate != nil {
		in, out := &in.Terminate, &out.Terminate
		*out = new(TLSConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLS.
func (in *TLS) DeepCopy() *TLS {
	if in == nil {
		return nil
	}
	out := new(TLS)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSCACertificate) DeepCopyInto(out *TLSCACertificate) {
	*out = *in
	if in.Certificate != nil {
		in, out := &in.Certificate, &out.Certificate
		*out = make([]byte, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSCACertificate.
func (in *TLSCACertificate) DeepCopy() *TLSCACertificate {
	if in == nil {
		return nil
	}
	out := new(TLSCACertificate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSCertificate) DeepCopyInto(out *TLSCertificate) {
	*out = *in
	if in.Certificate != nil {
		in, out := &in.Certificate, &out.Certificate
		*out = make([]byte, len(*in))
		copy(*out, *in)
	}
	if in.PrivateKey != nil {
		in, out := &in.PrivateKey, &out.PrivateKey
		*out = make(PrivateBytes, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSCertificate.
func (in *TLSCertificate) DeepCopy() *TLSCertificate {
	if in == nil {
		return nil
	}
	out := new(TLSCertificate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSConfig) DeepCopyInto(out *TLSConfig) {
	*out = *in
	if in.Certificates != nil {
		in, out := &in.Certificates, &out.Certificates
		*out = make([]TLSCertificate, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ClientCertificates != nil {
		in, out := &in.ClientCertificates, &out.ClientCertificates
		*out = make([]TLSCertificate, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.CACertificate != nil {
		in, out := &in.CACertificate, &out.CACertificate
		*out = new(TLSCACertificate)
		(*in).DeepCopyInto(*out)
	}
	if in.MinVersion != nil {
		in, out := &in.MinVersion, &out.MinVersion
		*out = new(TLSVersion)
		**out = **in
	}
	if in.MaxVersion != nil {
		in, out := &in.MaxVersion, &out.MaxVersion
		*out = new(TLSVersion)
		**out = **in
	}
	if in.Ciphers != nil {
		in, out := &in.Ciphers, &out.Ciphers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ECDHCurves != nil {
		in, out := &in.ECDHCurves, &out.ECDHCurves
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.SignatureAlgorithms != nil {
		in, out := &in.SignatureAlgorithms, &out.SignatureAlgorithms
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ALPNProtocols != nil {
		in, out := &in.ALPNProtocols, &out.ALPNProtocols
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSConfig.
func (in *TLSConfig) DeepCopy() *TLSConfig {
	if in == nil {
		return nil
	}
	out := new(TLSConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSInspectorConfig) DeepCopyInto(out *TLSInspectorConfig) {
	*out = *in
	if in.SNIs != nil {
		in, out := &in.SNIs, &out.SNIs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSInspectorConfig.
func (in *TLSInspectorConfig) DeepCopy() *TLSInspectorConfig {
	if in == nil {
		return nil
	}
	out := new(TLSInspectorConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TLSUpstreamConfig) DeepCopyInto(out *TLSUpstreamConfig) {
	*out = *in
	if in.SNI != nil {
		in, out := &in.SNI, &out.SNI
		*out = new(string)
		**out = **in
	}
	if in.CACertificate != nil {
		in, out := &in.CACertificate, &out.CACertificate
		*out = new(TLSCACertificate)
		(*in).DeepCopyInto(*out)
	}
	in.TLSConfig.DeepCopyInto(&out.TLSConfig)
	if in.SubjectAltNames != nil {
		in, out := &in.SubjectAltNames, &out.SubjectAltNames
		*out = make([]SubjectAltName, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TLSUpstreamConfig.
func (in *TLSUpstreamConfig) DeepCopy() *TLSUpstreamConfig {
	if in == nil {
		return nil
	}
	out := new(TLSUpstreamConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TextAccessLog) DeepCopyInto(out *TextAccessLog) {
	*out = *in
	if in.CELMatches != nil {
		in, out := &in.CELMatches, &out.CELMatches
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Format != nil {
		in, out := &in.Format, &out.Format
		*out = new(string)
		**out = **in
	}
	if in.LogType != nil {
		in, out := &in.LogType, &out.LogType
		*out = new(ProxyAccessLogType)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TextAccessLog.
func (in *TextAccessLog) DeepCopy() *TextAccessLog {
	if in == nil {
		return nil
	}
	out := new(TextAccessLog)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Timeout) DeepCopyInto(out *Timeout) {
	*out = *in
	if in.TCP != nil {
		in, out := &in.TCP, &out.TCP
		*out = new(TCPTimeout)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = new(HTTPTimeout)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Timeout.
func (in *Timeout) DeepCopy() *Timeout {
	if in == nil {
		return nil
	}
	out := new(Timeout)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Tracing) DeepCopyInto(out *Tracing) {
	*out = *in
	if in.CustomTags != nil {
		in, out := &in.CustomTags, &out.CustomTags
		*out = make(map[string]v1alpha1.CustomTag, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	in.Destination.DeepCopyInto(&out.Destination)
	if in.Traffic != nil {
		in, out := &in.Traffic, &out.Traffic
		*out = new(TrafficFeatures)
		(*in).DeepCopyInto(*out)
	}
	in.Provider.DeepCopyInto(&out.Provider)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Tracing.
func (in *Tracing) DeepCopy() *Tracing {
	if in == nil {
		return nil
	}
	out := new(Tracing)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TrafficFeatures) DeepCopyInto(out *TrafficFeatures) {
	*out = *in
	if in.RateLimit != nil {
		in, out := &in.RateLimit, &out.RateLimit
		*out = new(RateLimit)
		(*in).DeepCopyInto(*out)
	}
	if in.LoadBalancer != nil {
		in, out := &in.LoadBalancer, &out.LoadBalancer
		*out = new(LoadBalancer)
		(*in).DeepCopyInto(*out)
	}
	if in.ProxyProtocol != nil {
		in, out := &in.ProxyProtocol, &out.ProxyProtocol
		*out = new(ProxyProtocol)
		**out = **in
	}
	if in.HealthCheck != nil {
		in, out := &in.HealthCheck, &out.HealthCheck
		*out = new(HealthCheck)
		(*in).DeepCopyInto(*out)
	}
	if in.FaultInjection != nil {
		in, out := &in.FaultInjection, &out.FaultInjection
		*out = new(FaultInjection)
		(*in).DeepCopyInto(*out)
	}
	if in.CircuitBreaker != nil {
		in, out := &in.CircuitBreaker, &out.CircuitBreaker
		*out = new(CircuitBreaker)
		(*in).DeepCopyInto(*out)
	}
	if in.Timeout != nil {
		in, out := &in.Timeout, &out.Timeout
		*out = new(Timeout)
		(*in).DeepCopyInto(*out)
	}
	if in.TCPKeepalive != nil {
		in, out := &in.TCPKeepalive, &out.TCPKeepalive
		*out = new(TCPKeepalive)
		(*in).DeepCopyInto(*out)
	}
	if in.Retry != nil {
		in, out := &in.Retry, &out.Retry
		*out = new(Retry)
		(*in).DeepCopyInto(*out)
	}
	if in.BackendConnection != nil {
		in, out := &in.BackendConnection, &out.BackendConnection
		*out = new(BackendConnection)
		(*in).DeepCopyInto(*out)
	}
	if in.HTTP2 != nil {
		in, out := &in.HTTP2, &out.HTTP2
		*out = new(HTTP2Settings)
		(*in).DeepCopyInto(*out)
	}
	if in.DNS != nil {
		in, out := &in.DNS, &out.DNS
		*out = new(DNS)
		(*in).DeepCopyInto(*out)
	}
	if in.ResponseOverride != nil {
		in, out := &in.ResponseOverride, &out.ResponseOverride
		*out = new(ResponseOverride)
		(*in).DeepCopyInto(*out)
	}
	if in.Compression != nil {
		in, out := &in.Compression, &out.Compression
		*out = make([]*Compression, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(Compression)
				**out = **in
			}
		}
	}
	if in.HTTPUpgrade != nil {
		in, out := &in.HTTPUpgrade, &out.HTTPUpgrade
		*out = make([]HTTPUpgradeConfig, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Telemetry != nil {
		in, out := &in.Telemetry, &out.Telemetry
		*out = new(v1alpha1.BackendTelemetry)
		(*in).DeepCopyInto(*out)
	}
	if in.RequestBuffer != nil {
		in, out := &in.RequestBuffer, &out.RequestBuffer
		*out = new(RequestBuffer)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TrafficFeatures.
func (in *TrafficFeatures) DeepCopy() *TrafficFeatures {
	if in == nil {
		return nil
	}
	out := new(TrafficFeatures)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UDPListener) DeepCopyInto(out *UDPListener) {
	*out = *in
	in.CoreListenerDetails.DeepCopyInto(&out.CoreListenerDetails)
	if in.Route != nil {
		in, out := &in.Route, &out.Route
		*out = new(UDPRoute)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UDPListener.
func (in *UDPListener) DeepCopy() *UDPListener {
	if in == nil {
		return nil
	}
	out := new(UDPListener)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UDPRoute) DeepCopyInto(out *UDPRoute) {
	*out = *in
	if in.Destination != nil {
		in, out := &in.Destination, &out.Destination
		*out = new(RouteDestination)
		(*in).DeepCopyInto(*out)
	}
	if in.LoadBalancer != nil {
		in, out := &in.LoadBalancer, &out.LoadBalancer
		*out = new(LoadBalancer)
		(*in).DeepCopyInto(*out)
	}
	if in.DNS != nil {
		in, out := &in.DNS, &out.DNS
		*out = new(DNS)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UDPRoute.
func (in *UDPRoute) DeepCopy() *UDPRoute {
	if in == nil {
		return nil
	}
	out := new(UDPRoute)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *URLRewrite) DeepCopyInto(out *URLRewrite) {
	*out = *in
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(ExtendedHTTPPathModifier)
		(*in).DeepCopyInto(*out)
	}
	if in.Host != nil {
		in, out := &in.Host, &out.Host
		*out = new(HTTPHostModifier)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new URLRewrite.
func (in *URLRewrite) DeepCopy() *URLRewrite {
	if in == nil {
		return nil
	}
	out := new(URLRewrite)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *UnstructuredRef) DeepCopyInto(out *UnstructuredRef) {
	*out = *in
	if in.Object != nil {
		in, out := &in.Object, &out.Object
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new UnstructuredRef.
func (in *UnstructuredRef) DeepCopy() *UnstructuredRef {
	if in == nil {
		return nil
	}
	out := new(UnstructuredRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Wasm) DeepCopyInto(out *Wasm) {
	*out = *in
	if in.RootID != nil {
		in, out := &in.RootID, &out.RootID
		*out = new(string)
		**out = **in
	}
	if in.Config != nil {
		in, out := &in.Config, &out.Config
		*out = new(apiextensionsv1.JSON)
		(*in).DeepCopyInto(*out)
	}
	if in.Code != nil {
		in, out := &in.Code, &out.Code
		*out = new(HTTPWasmCode)
		**out = **in
	}
	if in.HostKeys != nil {
		in, out := &in.HostKeys, &out.HostKeys
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Wasm.
func (in *Wasm) DeepCopy() *Wasm {
	if in == nil {
		return nil
	}
	out := new(Wasm)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *XForwardedClientCert) DeepCopyInto(out *XForwardedClientCert) {
	*out = *in
	if in.CertDetailsToAdd != nil {
		in, out := &in.CertDetailsToAdd, &out.CertDetailsToAdd
		*out = make([]v1alpha1.XFCCCertData, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new XForwardedClientCert.
func (in *XForwardedClientCert) DeepCopy() *XForwardedClientCert {
	if in == nil {
		return nil
	}
	out := new(XForwardedClientCert)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Xds) DeepCopyInto(out *Xds) {
	*out = *in
	if in.ReadyListener != nil {
		in, out := &in.ReadyListener, &out.ReadyListener
		*out = new(ReadyListener)
		**out = **in
	}
	if in.AccessLog != nil {
		in, out := &in.AccessLog, &out.AccessLog
		*out = new(AccessLog)
		(*in).DeepCopyInto(*out)
	}
	if in.Tracing != nil {
		in, out := &in.Tracing, &out.Tracing
		*out = new(Tracing)
		(*in).DeepCopyInto(*out)
	}
	if in.Metrics != nil {
		in, out := &in.Metrics, &out.Metrics
		*out = new(Metrics)
		**out = **in
	}
	if in.HTTP != nil {
		in, out := &in.HTTP, &out.HTTP
		*out = make([]*HTTPListener, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(HTTPListener)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.TCP != nil {
		in, out := &in.TCP, &out.TCP
		*out = make([]*TCPListener, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(TCPListener)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.UDP != nil {
		in, out := &in.UDP, &out.UDP
		*out = make([]*UDPListener, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(UDPListener)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.EnvoyPatchPolicies != nil {
		in, out := &in.EnvoyPatchPolicies, &out.EnvoyPatchPolicies
		*out = make([]*EnvoyPatchPolicy, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(EnvoyPatchPolicy)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.FilterOrder != nil {
		in, out := &in.FilterOrder, &out.FilterOrder
		*out = make([]v1alpha1.FilterPosition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.GlobalResources != nil {
		in, out := &in.GlobalResources, &out.GlobalResources
		*out = new(GlobalResources)
		(*in).DeepCopyInto(*out)
	}
	if in.ExtensionServerPolicies != nil {
		in, out := &in.ExtensionServerPolicies, &out.ExtensionServerPolicies
		*out = make([]*UnstructuredRef, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(UnstructuredRef)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Xds.
func (in *Xds) DeepCopy() *Xds {
	if in == nil {
		return nil
	}
	out := new(Xds)
	in.DeepCopyInto(out)
	return out
}
