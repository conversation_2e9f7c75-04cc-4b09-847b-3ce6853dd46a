# Extension Server for InferencePool Custom Backend Support

This extension server implements custom backend support for InferencePool resources in Envoy Gateway. When an HTTPRoute uses an InferencePool as a backend reference, this extension server will:

1. Detect the InferencePool custom backend
2. Create an `original_destination_cluster` with ORIGINAL_DST type
3. Configure the cluster to use HTTP header routing with `target-pod` header
4. Modify the route to use the new cluster with a 120-second timeout

## Features

- **InferencePool Detection**: Automatically detects InferencePool custom backends using the API version `sigs.k8s.io/gateway-api-inference-extension/v1alpha2`
- **Dynamic Cluster Naming**: Creates cluster names in the format `inferencepool/{namespace}/{name}/{targetPortNumber}`
- **Original Destination Load Balancing**: Uses ORIGINAL_DST cluster type with HTTP header-based routing
- **Route Modification**: Updates routes to use the new cluster with extended timeout

## Implementation Details

### PostRouteModify Method

The `PostRouteModify` method is called by Envoy Gateway after generating a Route xDS configuration. It:

1. **Parses Extension Resources**: Checks for InferencePool resources in the route context
2. **Creates Custom Cluster**: Generates an original destination cluster with the configuration:
   ```go
   &clusterv3.Cluster{
       Name: "inferencepool/{namespace}/{name}/{targetPortNumber}",
       ClusterDiscoveryType: &clusterv3.Cluster_Type{
           Type: clusterv3.Cluster_ORIGINAL_DST,
       },
       LbPolicy: clusterv3.Cluster_CLUSTER_PROVIDED,
       ConnectTimeout: durationpb.New(6 * time.Second),
       DnsLookupFamily: clusterv3.Cluster_V4_ONLY,
       LbConfig: &clusterv3.Cluster_OriginalDstLbConfig_{
           OriginalDstLbConfig: &clusterv3.Cluster_OriginalDstLbConfig{
               UseHttpHeader: true,
               HttpHeaderName: "target-pod",
           },
       },
   }
   ```
3. **Modifies Route**: Updates the route to use the new cluster and sets timeout to 120 seconds

### Cluster Configuration

The generated cluster configuration matches the patch requirements:

```yaml
- type: "type.googleapis.com/envoy.config.cluster.v3.Cluster"
  name: "inferencepool/{namespace}/{name}/{targetPortNumber}"
  operation:
    op: add
    path: ""
    value:
      name: inferencepool/{namespace}/{name}/{targetPortNumber}
      type: ORIGINAL_DST  
      original_dst_lb_config:
        use_http_header: true
        http_header_name: "target-pod"
      connect_timeout: 6s
      lb_policy: CLUSTER_PROVIDED
      dns_lookup_family: V4_ONLY
```

### Route Configuration

The route is modified to use the new cluster:

```yaml
route:  
  cluster: inferencepool/{namespace}/{name}/{targetPortNumber}
  timeout: 120s
```

## Usage

1. **Build the Extension Server**:
   ```bash
   go build ./cmd/extension-server
   ```

2. **Deploy the Extension Server**: Deploy the server in your Kubernetes cluster

3. **Configure Envoy Gateway**: Set up the EnvoyProxy resource to use this extension server for PostRouteModify hooks

4. **Create HTTPRoute with InferencePool Backend**: Use InferencePool as a backend reference in your HTTPRoute

## Testing

Run the tests to verify the implementation:

```bash
go test ./internal/extensionserver -v
```

The tests cover:
- InferencePool detection and processing
- Cluster creation with correct configuration
- Route modification
- Error handling for invalid resources

## Dependencies

- `sigs.k8s.io/gateway-api-inference-extension/api/v1alpha2` - InferencePool API types
- Envoy Gateway extension API
- Envoy xDS API (cluster and route v3)
